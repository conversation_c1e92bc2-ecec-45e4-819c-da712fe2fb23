globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/team/detail/components/TeamDetailContent.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _services = __mako_require__("src/services/index.ts");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team/detail/components/TeamMemberList.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamDetailContent = ({ teamDetail, loading, onRefresh })=>{
                _s();
                const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
                const [updating, setUpdating] = (0, _react.useState)(false);
                const [deleting, setDeleting] = (0, _react.useState)(false);
                const [form] = _antd.Form.useForm();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                /**
   * 处理编辑团队信息操作
   *
   * 执行步骤：
   * 1. 验证团队详情数据存在
   * 2. 将当前团队信息填充到表单中
   * 3. 显示编辑模态框
   */ const handleEdit = ()=>{
                    if (!teamDetail) return;
                    // 将当前团队信息填充到表单中
                    form.setFieldsValue({
                        name: teamDetail.name,
                        description: teamDetail.description
                    });
                    setEditModalVisible(true);
                };
                /**
   * 处理团队信息更新操作
   *
   * 执行流程：
   * 1. 验证团队详情数据存在
   * 2. 设置更新状态，显示加载动画
   * 3. 构造更新请求数据
   * 4. 调用API更新团队信息
   * 5. 关闭编辑模态框并显示成功消息
   * 6. 刷新团队详情数据
   * 7. 处理错误情况
   *
   * @param values 表单提交的值，包含团队名称和描述
   */ const handleUpdate = async (values)=>{
                    if (!teamDetail) return;
                    try {
                        setUpdating(true);
                        // 构造更新请求数据
                        const updateData = {
                            name: values.name,
                            description: values.description
                        };
                        await _services.TeamService.updateCurrentTeam(updateData);
                        setEditModalVisible(false);
                        _antd.message.success('团队信息更新成功');
                        onRefresh(); // 刷新团队详情
                    } catch (error) {
                        console.error('更新团队信息失败:', error);
                        _antd.message.error('更新团队信息失败，请稍后重试');
                    } finally{
                        setUpdating(false);
                    }
                };
                /**
   * 处理删除团队操作
   *
   * 执行流程：
   * 1. 显示确认对话框，详细说明删除后果
   * 2. 用户确认后调用删除API
   * 3. 清除本地团队状态和Token
   * 4. 跳转到团队选择页面
   * 5. 处理错误情况
   *
   * 安全措施：
   * - 只有团队创建者可以看到删除按钮
   * - 二次确认防止误操作
   * - 详细说明删除后果
   */ const handleDelete = ()=>{
                    if (!teamDetail) return;
                    _antd.Modal.confirm({
                        title: '确认删除团队',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 125,
                            columnNumber: 13
                        }, this),
                        content: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                    children: [
                                        "您确定要删除团队 ",
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                            children: [
                                                '"',
                                                teamDetail.name,
                                                '"'
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 128,
                                            columnNumber: 23
                                        }, this),
                                        " 吗？"
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 128,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                    style: {
                                        color: '#ff4d4f',
                                        marginBottom: 0
                                    },
                                    children: "⚠️ 此操作不可撤销，删除后将："
                                }, void 0, false, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 129,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                                    style: {
                                        color: '#ff4d4f',
                                        marginTop: 8,
                                        paddingLeft: 20
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "永久删除团队及所有相关数据"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 133,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "移除所有团队成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 134,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "无法恢复团队信息"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 135,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 132,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 127,
                            columnNumber: 9
                        }, this),
                        okText: '确认删除',
                        okType: 'danger',
                        cancelText: '取消',
                        confirmLoading: deleting,
                        onOk: async ()=>{
                            try {
                                setDeleting(true);
                                await _services.TeamService.deleteCurrentTeam();
                                _antd.message.success('团队删除成功');
                                // 清除当前团队状态并跳转到团队选择页面
                                await setInitialState((s)=>({
                                        ...s,
                                        currentTeam: null
                                    }));
                                _services.AuthService.clearTeamToken();
                                _max.history.push('/user/team-select');
                            } catch (error) {
                                console.error('删除团队失败:', error);
                                _antd.message.error('删除团队失败，请稍后重试');
                            } finally{
                                setDeleting(false);
                            }
                        }
                    });
                };
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        textAlign: 'center',
                        padding: '50px 0'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        size: "large"
                    }, void 0, false, {
                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                        lineNumber: 166,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 165,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    description: "请先选择一个团队"
                }, void 0, false, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 173,
                    columnNumber: 7
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        marginBottom: 16
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#1890ff'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 3,
                                                    style: {
                                                        margin: 0
                                                    },
                                                    children: teamDetail.name
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 187,
                                                    columnNumber: 13
                                                }, this),
                                                teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 14
                                                    },
                                                    children: "(管理员)"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 185,
                                            columnNumber: 11
                                        }, this),
                                        teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 199,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    onClick: handleEdit,
                                                    children: "编辑团队"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 198,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    danger: true,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                        lineNumber: 206,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    onClick: handleDelete,
                                                    loading: deleting,
                                                    children: "删除团队"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 204,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 197,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 184,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                                    column: 2,
                                    bordered: true,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                            label: "团队名称",
                                            children: teamDetail.name
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 217,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                            label: "成员数量",
                                            children: [
                                                teamDetail.memberCount,
                                                " 人"
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 220,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                            label: "创建时间",
                                            children: new Date(teamDetail.createdAt).toLocaleString()
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 223,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                            label: "更新时间",
                                            children: new Date(teamDetail.updatedAt).toLocaleString()
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 226,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                            label: "团队描述",
                                            span: 2,
                                            children: teamDetail.description || '暂无描述'
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 229,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                    lineNumber: 216,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 183,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 235,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberList.default, {
                            teamId: teamDetail.id,
                            isCreator: teamDetail.isCreator
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 238,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "编辑团队信息",
                            open: editModalVisible,
                            onCancel: ()=>setEditModalVisible(false),
                            footer: null,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: form,
                                layout: "vertical",
                                onFinish: handleUpdate,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队名称",
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入团队名称'
                                            },
                                            {
                                                min: 2,
                                                max: 50,
                                                message: '团队名称长度应在2-50个字符之间'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入团队名称"
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 260,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 252,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: "团队描述",
                                        name: "description",
                                        rules: [
                                            {
                                                max: 200,
                                                message: '团队描述不能超过200个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 4,
                                            placeholder: "请输入团队描述（可选）",
                                            showCount: true,
                                            maxLength: 200
                                        }, void 0, false, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 270,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 263,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        style: {
                                            marginBottom: 0,
                                            textAlign: 'right'
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setEditModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 280,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: updating,
                                                    children: "保存"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                            lineNumber: 279,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                        lineNumber: 278,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                                lineNumber: 247,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                            lineNumber: 241,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/detail/components/TeamDetailContent.tsx",
                    lineNumber: 181,
                    columnNumber: 5
                }, this);
            };
            _s(TeamDetailContent, "UalB0vhOvdT9JI/tG4E1rE8Z8wk=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = TeamDetailContent;
            var _default = TeamDetailContent;
            var _c;
            $RefreshReg$(_c, "TeamDetailContent");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '5229063865666715875';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "common",
            "p__subscription__index"
        ],
        "src/pages/team/create/index.tsx": [
            "p__team__create__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "common",
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.17044119200650418717.hot-update.js.map