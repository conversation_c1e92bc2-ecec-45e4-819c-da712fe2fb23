{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.17044119200650418717.hot-update.js", "src/pages/team/detail/components/TeamDetailContent.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='5229063865666715875';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"common\",\"p__subscription__index\"],\"src/pages/team/create/index.tsx\":[\"p__team__create__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"common\",\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队详情内容组件\n */\n\nimport React, { useState } from 'react';\nimport {\n  Descriptions,\n  Button,\n  Space,\n  Typography,\n  message,\n  Modal,\n  Form,\n  Input,\n  Spin,\n  Divider,\n  Empty\n} from 'antd';\nimport {\n  TeamOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { TeamService, AuthService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport { history, useModel } from '@umijs/max';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamDetailContentProps {\n  teamDetail: TeamDetailResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n}\n\nconst TeamDetailContent: React.FC<TeamDetailContentProps> = ({\n  teamDetail,\n  loading,\n  onRefresh\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  /**\n   * 处理编辑团队信息操作\n   *\n   * 执行步骤：\n   * 1. 验证团队详情数据存在\n   * 2. 将当前团队信息填充到表单中\n   * 3. 显示编辑模态框\n   */\n  const handleEdit = () => {\n    if (!teamDetail) return;\n    // 将当前团队信息填充到表单中\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description,\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 处理团队信息更新操作\n   *\n   * 执行流程：\n   * 1. 验证团队详情数据存在\n   * 2. 设置更新状态，显示加载动画\n   * 3. 构造更新请求数据\n   * 4. 调用API更新团队信息\n   * 5. 关闭编辑模态框并显示成功消息\n   * 6. 刷新团队详情数据\n   * 7. 处理错误情况\n   *\n   * @param values 表单提交的值，包含团队名称和描述\n   */\n  const handleUpdate = async (values: any) => {\n    if (!teamDetail) return;\n\n    try {\n      setUpdating(true);\n      // 构造更新请求数据\n      const updateData: UpdateTeamRequest = {\n        name: values.name,\n        description: values.description,\n      };\n\n      await TeamService.updateCurrentTeam(updateData);\n      setEditModalVisible(false);\n      message.success('团队信息更新成功');\n      onRefresh(); // 刷新团队详情\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n      message.error('更新团队信息失败，请稍后重试');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  /**\n   * 处理删除团队操作\n   *\n   * 执行流程：\n   * 1. 显示确认对话框，详细说明删除后果\n   * 2. 用户确认后调用删除API\n   * 3. 清除本地团队状态和Token\n   * 4. 跳转到团队选择页面\n   * 5. 处理错误情况\n   *\n   * 安全措施：\n   * - 只有团队创建者可以看到删除按钮\n   * - 二次确认防止误操作\n   * - 详细说明删除后果\n   */\n  const handleDelete = () => {\n    if (!teamDetail) return;\n\n    Modal.confirm({\n      title: '确认删除团队',\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>您确定要删除团队 <strong>\"{teamDetail.name}\"</strong> 吗？</p>\n          <p style={{ color: '#ff4d4f', marginBottom: 0 }}>\n            ⚠️ 此操作不可撤销，删除后将：\n          </p>\n          <ul style={{ color: '#ff4d4f', marginTop: 8, paddingLeft: 20 }}>\n            <li>永久删除团队及所有相关数据</li>\n            <li>移除所有团队成员</li>\n            <li>无法恢复团队信息</li>\n          </ul>\n        </div>\n      ),\n      okText: '确认删除',\n      okType: 'danger',\n      cancelText: '取消',\n      confirmLoading: deleting,\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n\n          // 清除当前团队状态并跳转到团队选择页面\n          await setInitialState((s) => ({ ...s, currentTeam: null }));\n          AuthService.clearTeamToken();\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败，请稍后重试');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"请先选择一个团队\"\n      />\n    );\n  }\n\n  return (\n    <div>\n      {/* 团队基本信息 */}\n      <div style={{ marginBottom: 24 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\n          <Space>\n            <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />\n            <Title level={3} style={{ margin: 0 }}>\n              {teamDetail.name}\n            </Title>\n            {teamDetail.isCreator && (\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>\n                (管理员)\n              </Text>\n            )}\n          </Space>\n          {teamDetail.isCreator && (\n            <Space>\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n              >\n                编辑团队\n              </Button>\n              <Button\n                danger\n                icon={<DeleteOutlined />}\n                onClick={handleDelete}\n                loading={deleting}\n              >\n                删除团队\n              </Button>\n            </Space>\n          )}\n        </div>\n\n        <Descriptions column={2} bordered>\n          <Descriptions.Item label=\"团队名称\">\n            {teamDetail.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"成员数量\">\n            {teamDetail.memberCount} 人\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {new Date(teamDetail.createdAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">\n            {new Date(teamDetail.updatedAt).toLocaleString()}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"团队描述\" span={2}>\n            {teamDetail.description || '暂无描述'}\n          </Descriptions.Item>\n        </Descriptions>\n      </div>\n\n      <Divider />\n\n      {/* 团队成员列表 */}\n      <TeamMemberList teamId={teamDetail.id} isCreator={teamDetail.isCreator} />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdate}\n        >\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' }\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[\n              { max: 200, message: '团队描述不能超过200个字符' }\n            ]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n\n    </div>\n  );\n};\n\nexport default TeamDetailContent;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCoSb;;;2BAAA;;;;;;;oFAnSgC;yCAazB;0CAMA;6CACkC;wCAEP;4FACP;;;;;;;;;;YAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAQ1B,MAAM,oBAAsD,CAAC,EAC3D,UAAU,EACV,OAAO,EACP,SAAS,EACV;;gBACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAErC;;;;;;;GAOC,GACD,MAAM,aAAa;oBACjB,IAAI,CAAC,YAAY;oBACjB,gBAAgB;oBAChB,KAAK,cAAc,CAAC;wBAClB,MAAM,WAAW,IAAI;wBACrB,aAAa,WAAW,WAAW;oBACrC;oBACA,oBAAoB;gBACtB;gBAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,eAAe,OAAO;oBAC1B,IAAI,CAAC,YAAY;oBAEjB,IAAI;wBACF,YAAY;wBACZ,WAAW;wBACX,MAAM,aAAgC;4BACpC,MAAM,OAAO,IAAI;4BACjB,aAAa,OAAO,WAAW;wBACjC;wBAEA,MAAM,qBAAW,CAAC,iBAAiB,CAAC;wBACpC,oBAAoB;wBACpB,aAAO,CAAC,OAAO,CAAC;wBAChB,aAAa,SAAS;oBACxB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,YAAY;oBACd;gBACF;gBAEA;;;;;;;;;;;;;;GAcC,GACD,MAAM,eAAe;oBACnB,IAAI,CAAC,YAAY;oBAEjB,WAAK,CAAC,OAAO,CAAC;wBACZ,OAAO;wBACP,oBAAM,2BAAC,gCAAyB;;;;;wBAChC,uBACE,2BAAC;;8CACC,2BAAC;;wCAAE;sDAAS,2BAAC;;gDAAO;gDAAE,WAAW,IAAI;gDAAC;;;;;;;wCAAU;;;;;;;8CAChD,2BAAC;oCAAE,OAAO;wCAAE,OAAO;wCAAW,cAAc;oCAAE;8CAAG;;;;;;8CAGjD,2BAAC;oCAAG,OAAO;wCAAE,OAAO;wCAAW,WAAW;wCAAG,aAAa;oCAAG;;sDAC3D,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAIV,QAAQ;wBACR,QAAQ;wBACR,YAAY;wBACZ,gBAAgB;wBAChB,MAAM;4BACJ,IAAI;gCACF,YAAY;gCACZ,MAAM,qBAAW,CAAC,iBAAiB;gCACnC,aAAO,CAAC,OAAO,CAAC;gCAEhB,qBAAqB;gCACrB,MAAM,gBAAgB,CAAC,IAAO,CAAA;wCAAE,GAAG,CAAC;wCAAE,aAAa;oCAAK,CAAA;gCACxD,qBAAW,CAAC,cAAc;gCAC1B,YAAO,CAAC,IAAI,CAAC;4BACf,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,WAAW;gCACzB,aAAO,CAAC,KAAK,CAAC;4BAChB,SAAU;gCACR,YAAY;4BACd;wBACF;oBACF;gBACF;gBAEA,IAAI,SACF,qBACE,2BAAC;oBAAI,OAAO;wBAAE,WAAW;wBAAU,SAAS;oBAAS;8BACnD,cAAA,2BAAC,UAAI;wBAAC,MAAK;;;;;;;;;;;gBAKjB,IAAI,CAAC,YACH,qBACE,2BAAC,WAAK;oBACJ,OAAO,WAAK,CAAC,sBAAsB;oBACnC,aAAY;;;;;;gBAKlB,qBACE,2BAAC;;sCAEC,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,2BAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,gBAAgB;wCAAiB,YAAY;wCAAU,cAAc;oCAAG;;sDACrG,2BAAC,WAAK;;8DACJ,2BAAC,mBAAY;oDAAC,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAU;;;;;;8DACtD,2BAAC;oDAAM,OAAO;oDAAG,OAAO;wDAAE,QAAQ;oDAAE;8DACjC,WAAW,IAAI;;;;;;gDAEjB,WAAW,SAAS,kBACnB,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;;;;;;;wCAKnD,WAAW,SAAS,kBACnB,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,SAAS;8DACV;;;;;;8DAGD,2BAAC,YAAM;oDACL,MAAM;oDACN,oBAAM,2BAAC,qBAAc;;;;;oDACrB,SAAS;oDACT,SAAS;8DACV;;;;;;;;;;;;;;;;;;8CAOP,2BAAC,kBAAY;oCAAC,QAAQ;oCAAG,QAAQ;;sDAC/B,2BAAC,kBAAY,CAAC,IAAI;4CAAC,OAAM;sDACtB,WAAW,IAAI;;;;;;sDAElB,2BAAC,kBAAY,CAAC,IAAI;4CAAC,OAAM;;gDACtB,WAAW,WAAW;gDAAC;;;;;;;sDAE1B,2BAAC,kBAAY,CAAC,IAAI;4CAAC,OAAM;sDACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;sDAEhD,2BAAC,kBAAY,CAAC,IAAI;4CAAC,OAAM;sDACtB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;sDAEhD,2BAAC,kBAAY,CAAC,IAAI;4CAAC,OAAM;4CAAO,MAAM;sDACnC,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;sCAKjC,2BAAC,aAAO;;;;;sCAGR,2BAAC,uBAAc;4BAAC,QAAQ,WAAW,EAAE;4BAAE,WAAW,WAAW,SAAS;;;;;;sCAGtE,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,oBAAoB;4BACpC,QAAQ;sCAER,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;4CACrC;gDAAE,KAAK;gDAAG,KAAK;gDAAI,SAAS;4CAAoB;yCACjD;kDAED,cAAA,2BAAC,WAAK;4CAAC,aAAY;;;;;;;;;;;kDAGrB,2BAAC,UAAI,CAAC,IAAI;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAK,SAAS;4CAAiB;yCACvC;kDAED,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;4CACZ,SAAS;4CACT,WAAW;;;;;;;;;;;kDAIf,2BAAC,UAAI,CAAC,IAAI;wCAAC,OAAO;4CAAE,cAAc;4CAAG,WAAW;wCAAQ;kDACtD,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDAAC,SAAS,IAAM,oBAAoB;8DAAQ;;;;;;8DAGnD,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,SAAS;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW1E;eA/PM;;oBAQW,UAAI,CAAC;oBACQ,aAAQ;;;iBAThC;gBAiQN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDpSD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;YAAS;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;QAAC,wCAAuC;YAAC;SAAuC;IAAA;;AACr4B"}